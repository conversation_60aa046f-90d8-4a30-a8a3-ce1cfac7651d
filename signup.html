<!DOCTYPE html>
<html lang="en-GB">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - RestroManager</title>

    <meta name="description" content="Complete restaurant management solution for orders, menu, tasks, and analytics">
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="RestroManager">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="msapplication-config" content="/browserconfig.xml">

    <link rel="manifest" href="./manifest.json">

    <link rel="icon" type="image/png" sizes="32x32" href="./icons/icon-72x72.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./icons/icon-72x72.png">
    <link rel="apple-touch-icon" sizes="180x180" href="./icons/icon-192x192.png">
    <link rel="mask-icon" href="./icons/icon-192x192.png" color="#2563eb">

    <script src="https://cdn.tailwindcss.com"></script>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <script src="https://unpkg.com/lucide@latest"></script>

    <script src="./pwa-utils.js"></script>

    <style>
:root {
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --success-500: #10b981;
    --error-500: #ef4444;
    --warning-500: #f59e0b;
    --neutral-50: #f8fafc;
    --neutral-100: #f1f5f9;
    --neutral-200: #e5e7eb;
    --neutral-300: #d1d5db;
    --neutral-600: #4b5563;
    --neutral-800: #1f2937;
}

body {
    font-family: 'Inter', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: linear-gradient(145deg, var(--neutral-100) 0%, #e0e7ff 100%);
    margin: 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-container {
    background: white;
    box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15), 0 10px 20px -5px rgba(0, 0, 0, 0.1);
    border-radius: 24px;
    overflow: hidden;
    margin: 2rem;
    max-width: 1200px;
}

.top-panel {
    background: linear-gradient(145deg, white 0%, var(--neutral-50) 100%);
    border-bottom: 1px solid var(--neutral-200);
    padding: 2rem 3rem;
    position: relative;
}

.step-section {
    display: none;
    animation: fadeInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 2rem 3rem;
}

.step-section.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-indicator {
    position: relative;
    flex: 1;
    text-align: center;
    transition: all 0.3s ease;
}

.step-indicator .step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: white;
    border: 2px solid var(--neutral-200);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1rem;
    color: var(--neutral-600);
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.step-indicator.active .step-circle {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    border-color: var(--primary-600);
    color: white;
    transform: scale(1.15);
    box-shadow: 0 4px 16px rgba(37, 99, 235, 0.3);
}

.step-indicator.completed .step-circle {
    background: linear-gradient(135deg, var(--success-500), #059669);
    border-color: var(--success-500);
    color: white;
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
}

.step-indicator .step-label {
    color: var(--neutral-600);
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.75rem;
    transition: all 0.3s ease;
}

.step-indicator.active .step-label,
.step-indicator.completed .step-label {
    color: var(--neutral-800);
    font-weight: 600;
}

.step-connector {
    height: 3px;
    background: var(--neutral-200);
    position: absolute;
    top: 20px;
    left: 50%;
    width: 100%;
    transform: translateY(-50%);
    transition: all 0.3s ease;
}

.step-indicator.completed + .step-connector {
    background: linear-gradient(to right, var(--success-500), #059669);
}

.step-indicator.active + .step-connector {
    background: linear-gradient(to right, var(--primary-600), var(--neutral-200));
}

.form-input {
    border-radius: 12px;
    border: 1px solid var(--neutral-200);
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: white;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-input:focus {
    border-color: var(--primary-600);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    outline: none;
    transform: translateY(-1px);
}

.form-input.error {
    border-color: var(--error-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.form-input.success {
    border-color: var(--success-500);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

.error-message {
    color: var(--error-500);
    font-size: 0.75rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    animation: slideDown 0.3s ease-out;
}

.success-message {
    color: var(--success-500);
    font-size: 0.75rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    animation: slideDown 0.3s ease-out;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-700), #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
}

.btn-primary:disabled {
    background: var(--neutral-300);
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.btn-secondary {
    background: transparent;
    border: 1px solid var(--neutral-200);
    color: var(--neutral-600);
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: var(--neutral-50);
    border-color: var(--neutral-300);
    color: var(--neutral-800);
    transform: translateY(-1px);
}

.selectable-option input:checked + label {
    border-color: var(--primary-600);
    background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
    color: var(--primary-700);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
    transform: translateY(-2px);
}

.selectable-option label {
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.3s ease;
    border: 1px solid var(--neutral-200);
}

.selectable-option label:hover {
    border-color: var(--primary-100);
    transform: translateY(-1px);
    background: var(--neutral-50);
}

.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid white;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.drag-drop-area {
    border: 2px dashed var(--neutral-200);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.drag-drop-area:hover,
.drag-drop-area.dragover {
    background: var(--neutral-50);
    border-color: var(--primary-600);
}

#logo-preview {
    border-radius: 12px;
    border: 1px solid var(--neutral-200);
    background: var(--neutral-50);
}

.price-slider-track {
    background: var(--neutral-200);
    height: 4px;
    border-radius: 2px;
}

.price-slider-range {
    background: var(--primary-600);
    height: 4px;
    border-radius: 2px;
}

.price-slider-thumb::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: white;
    border: 2px solid var(--primary-600);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.price-slider-thumb::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: white;
    border: 2px solid var(--primary-600);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#floor-plan-preview {
    border-radius: 12px;
    border: 2px dashed var(--neutral-200);
    background: linear-gradient(145deg, var(--neutral-50), white);
}

.table-section,
.day-hours-row,
#summary-container > div {
    border-radius: 12px;
    border: 1px solid var(--neutral-200);
    background: white;
}

.table-row {
    border-radius: 8px;
    border: 1px solid var(--neutral-200);
}

#weekly-hours-container .day-hours-row:hover {
    background: var(--neutral-50);
}

#add-section-modal {
    backdrop-filter: blur(4px);
}

#add-section-modal .bg-white {
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

@media (max-width: 640px) {
    .main-container {
        margin: 1rem;
        border-radius: 16px;
    }

    .top-panel,
    .step-section {
        padding: 1.5rem;
    }

    .step-indicator .step-circle {
        width: 32px;
        height: 32px;
        font-size: 0.875rem;
    }

    .step-indicator .step-label {
        font-size: 0.65rem;
    }

    .btn-primary,
    .btn-secondary {
        padding: 0.75rem 1rem;
    }

    .form-input {
        padding: 0.65rem 0.9rem;
    }
}
    </style>
</head>
<body class="bg-slate-100">

    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-5xl flex flex-col main-container rounded-2xl overflow-hidden my-8">
            <div class="w-full top-panel p-6 sm:p-8">
                <div class="flex justify-between items-center mb-6">
                    <a href="#" class="flex items-center gap-3 group">
                        <div class="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg group-hover:shadow-xl transition-all duration-300">
                            <i data-lucide="chef-hat" class="w-6 h-6 text-white"></i>
                        </div>
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">RestroManager</h1>
                    </a>
                    <div class="hidden sm:block text-sm text-slate-500" id="progress-text">Step 1 of 7 - Account Details</div>
                </div>

                <div id="step-indicators-container" class="w-full flex items-center">
                    </div>
            </div>

            <div class="w-full p-6 sm:p-12">
                <form id="signup-form">
                    <!-- Step 1: Account Details -->
                    <div id="step-1" class="step-section active">
                        <div class="max-w-4xl mx-auto">
                            <div class="mb-10">
                                <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Create your account</h2>
                                <p class="text-slate-500 text-lg">Start by setting up your login details.</p>
                            </div>

                            <div class="space-y-8">
                                <button type="button" class="w-full flex items-center justify-center gap-3 py-3.5 border border-slate-300 rounded-xl hover:bg-slate-50 transition-all duration-300 hover:shadow-md hover:border-slate-400 group">
                                    <img src="https://www.google.com/favicon.ico" alt="Google icon" class="w-5 h-5 group-hover:scale-110 transition-transform">
                                    <span class="text-sm font-medium text-slate-700">Sign up with Google</span>
                                </button>

                                <div class="flex items-center">
                                    <div class="flex-grow border-t border-slate-200"></div>
                                    <span class="flex-shrink mx-4 text-slate-400 text-sm font-medium">OR</span>
                                    <div class="flex-grow border-t border-slate-200"></div>
                                </div>

                                <div class="space-y-6">
                                    <div class="form-group">
                                        <label for="email" class="block text-sm font-semibold text-slate-700 mb-2">Email Address</label>
                                        <div class="relative">
                                            <i data-lucide="mail" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400 transition-colors"></i>
                                            <input type="email" id="email" name="email" required placeholder="Enter your email address"
                                                   class="form-input pl-12 block w-full px-4 py-3 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        </div>
                                        <div class="validation-message"></div>
                                    </div>

                                    <div class="form-group">
                                        <label for="password" class="block text-sm font-semibold text-slate-700 mb-2">Password</label>
                                        <div class="relative">
                                            <i data-lucide="lock" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400 transition-colors"></i>
                                            <input type="password" id="password" name="password" required placeholder="Create a strong password (min. 8 characters)"
                                                   class="form-input pl-12 pr-12 block w-full px-4 py-3 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                            <button type="button" class="toggle-password absolute right-4 top-1/2 -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors">
                                                <i data-lucide="eye" class="w-5 h-5"></i>
                                            </button>
                                        </div>
                                        <div class="validation-message"></div>
                                        <div class="password-strength mt-3 hidden">
                                            <div class="flex gap-1 mb-2">
                                                <div class="strength-bar h-1.5 bg-slate-200 rounded flex-1"></div>
                                                <div class="strength-bar h-1.5 bg-slate-200 rounded flex-1"></div>
                                                <div class="strength-bar h-1.5 bg-slate-200 rounded flex-1"></div>
                                                <div class="strength-bar h-1.5 bg-slate-200 rounded flex-1"></div>
                                            </div>
                                            <p class="strength-text text-xs text-slate-500"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-12 flex sm:justify-end">
                                <button type="button" data-next="1" class="w-full sm:w-auto next-btn btn-primary px-8 py-3 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <span class="btn-text">Continue</span>
                                    <span class="btn-loading hidden"><span class="loading-spinner"></span> Processing...</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Business Details -->
                    <div id="step-2" class="step-section">
                        <div class="max-w-4xl mx-auto">
                            <div class="mb-10">
                                <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Tell us about your business</h2>
                                <p class="text-slate-500 text-lg">This information helps us tailor your experience.</p>
                            </div>

                            <div class="space-y-8">
                                <!-- Business Logo Upload -->
                                <div class="form-group">
                                    <label class="block text-sm font-semibold text-slate-700 mb-3">Business Logo (Optional)</label>
                                    <div class="drag-drop-area" id="logo-upload">
                                        <i data-lucide="upload-cloud" class="w-12 h-12 text-slate-400 mx-auto mb-3"></i>
                                        <p class="text-slate-600 font-medium">Drop your logo here or click to browse</p>
                                        <p class="text-xs text-slate-400 mt-1">PNG, JPG up to 5MB</p>
                                        <input type="file" id="logo-file" accept="image/*" class="hidden">
                                    </div>
                                    <div id="logo-preview" class="hidden mt-4 p-4 bg-slate-50 rounded-xl">
                                        <div class="flex items-center gap-3">
                                            <img id="logo-img" class="w-16 h-16 object-cover rounded-lg" alt="Logo preview">
                                            <div class="flex-1">
                                                <p class="font-medium text-slate-700" id="logo-name"></p>
                                                <p class="text-sm text-slate-500" id="logo-size"></p>
                                            </div>
                                            <button type="button" id="remove-logo" class="text-red-500 hover:text-red-700">
                                                <i data-lucide="x" class="w-5 h-5"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Business Information Grid -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="form-group md:col-span-2">
                                        <label for="business-name" class="block text-sm font-semibold text-slate-700 mb-2">Business Name</label>
                                        <div class="relative">
                                            <i data-lucide="store" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                            <input type="text" id="business-name" name="businessName" required
                                                   class="form-input pl-12 block w-full px-4 py-3 border border-slate-300 rounded-xl shadow-sm"
                                                   placeholder="Enter your business name">
                                        </div>
                                        <div class="validation-message"></div>
                                    </div>

                                    <div class="form-group">
                                        <label for="business-type" class="block text-sm font-semibold text-slate-700 mb-2">Business Type</label>
                                        <div class="relative">
                                            <i data-lucide="utensils" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                            <select id="business-type" name="businessType"
                                                    class="form-input pl-12 block w-full px-4 py-3 border border-slate-300 bg-white rounded-xl shadow-sm">
                                                <option value="">Select business type</option>
                                                <option value="Fine Dining">🍽️ Fine Dining</option>
                                                <option value="Casual Dining">🍕 Casual Dining</option>
                                                <option value="Cafe / Bistro">☕ Cafe / Bistro</option>
                                                <option value="Quick Service (QSR)">🍔 Quick Service (QSR)</option>
                                                <option value="Food Truck">🚚 Food Truck</option>
                                                <option value="Pub / Bar">🍺 Pub / Bar</option>
                                                <option value="Bakery">🥖 Bakery</option>
                                            </select>
                                        </div>
                                        <div class="validation-message"></div>
                                    </div>

                                    <div class="form-group">
                                        <label for="annual-revenue" class="block text-sm font-semibold text-slate-700 mb-2">Estimated Annual Revenue</label>
                                        <div class="relative">
                                            <i data-lucide="trending-up" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                            <select id="annual-revenue" name="annualRevenue"
                                                    class="form-input pl-12 block w-full px-4 py-3 border border-slate-300 bg-white rounded-xl shadow-sm">
                                                <option value="">Select revenue range</option>
                                                <option value="Less than £50k">💰 Less than £50,000</option>
                                                <option value="£50k - £250k">💎 £50k - £250k</option>
                                                <option value="£250k - £1M">🏆 £250k - £1M</option>
                                                <option value="More than £1M">🚀 More than £1M</option>
                                            </select>
                                        </div>
                                        <div class="validation-message"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-12 flex flex-col-reverse sm:flex-row sm:justify-between items-center gap-4">
                                <button type="button" data-prev="0" class="w-full sm:w-auto prev-btn btn-secondary px-6 py-3 text-sm font-semibold rounded-xl">Back</button>
                                <button type="button" data-next="2" class="w-full sm:w-auto next-btn btn-primary px-8 py-3 text-sm font-semibold text-white rounded-xl">
                                    <span class="btn-text">Next Step</span>
                                    <span class="btn-loading hidden"><span class="loading-spinner"></span> Processing...</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step 3: Cuisine & Operations -->
                    <div id="step-3" class="step-section">
                        <div class="max-w-4xl mx-auto">
                            <div class="mb-10">
                                <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Cuisine & Operations</h2>
                                <p class="text-slate-500 text-lg">Specify your offerings and opening times.</p>
                            </div>

                            <div class="space-y-12">
                                <!-- Cuisine Selection -->
                                <div class="space-y-6">
                                    <div class="form-group">
                                        <label class="block text-sm font-semibold text-slate-700 mb-4">Cuisine Type (select all that apply)</label>
                                        <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
                                            <div class="selectable-option">
                                                <input type="checkbox" id="cuisine-british" name="cuisine" value="British" class="hidden">
                                                <label for="cuisine-british" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-slate-300">
                                                    <div class="text-3xl mb-2">🇬🇧</div>
                                                    <div class="text-sm">British</div>
                                                </label>
                                            </div>
                                            <div class="selectable-option">
                                                <input type="checkbox" id="cuisine-indian" name="cuisine" value="Indian" class="hidden">
                                                <label for="cuisine-indian" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-slate-300">
                                                    <div class="text-3xl mb-2">🍛</div>
                                                    <div class="text-sm">Indian</div>
                                                </label>
                                            </div>
                                            <div class="selectable-option">
                                                <input type="checkbox" id="cuisine-italian" name="cuisine" value="Italian" class="hidden">
                                                <label for="cuisine-italian" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-slate-300">
                                                    <div class="text-3xl mb-2">🍝</div>
                                                    <div class="text-sm">Italian</div>
                                                </label>
                                            </div>
                                            <div class="selectable-option">
                                                <input type="checkbox" id="cuisine-chinese" name="cuisine" value="Chinese" class="hidden">
                                                <label for="cuisine-chinese" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-slate-300">
                                                    <div class="text-3xl mb-2">🥢</div>
                                                    <div class="text-sm">Chinese</div>
                                                </label>
                                            </div>
                                            <div class="selectable-option">
                                                <input type="checkbox" id="cuisine-american" name="cuisine" value="American" class="hidden">
                                                <label for="cuisine-american" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-slate-300">
                                                    <div class="text-3xl mb-2">🍔</div>
                                                    <div class="text-sm">American</div>
                                                </label>
                                            </div>
                                            <div class="selectable-option">
                                                <input type="checkbox" id="cuisine-other" name="cuisine" value="Other" class="hidden">
                                                <label for="cuisine-other" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-slate-300">
                                                    <div class="text-3xl mb-2">🍽️</div>
                                                    <div class="text-sm">Other</div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Price Range -->
                                    <div class="form-group bg-slate-50 p-6 rounded-xl">
                                        <div class="flex justify-between items-center mb-4">
                                            <label class="block text-sm font-semibold text-slate-700">Price Range</label>
                                            <div class="text-lg font-semibold text-slate-800 bg-white px-4 py-2 rounded-lg shadow-sm">
                                                <span id="min-price-display">£10</span> - <span id="max-price-display">£80</span>
                                            </div>
                                        </div>
                                        <div class="relative h-12">
                                            <div class="price-slider-track absolute bg-slate-200 h-2 w-full top-1/2 -translate-y-1/2 rounded-full"></div>
                                            <div id="price-slider-range" class="absolute bg-blue-500 h-2 top-1/2 -translate-y-1/2 rounded-full"></div>
                                            <input type="range" id="min-price" name="minPrice" min="0" max="100" value="10"
                                                   class="price-slider-thumb absolute w-full h-2 top-1/2 -translate-y-1/2 appearance-none bg-transparent pointer-events-none">
                                            <input type="range" id="max-price" name="maxPrice" min="0" max="100" value="80"
                                                   class="price-slider-thumb absolute w-full h-2 top-1/2 -translate-y-1/2 appearance-none bg-transparent pointer-events-none">
                                        </div>
                                    </div>
                                </div>

                                <!-- Operating Hours -->
                                <div class="bg-slate-50 p-6 rounded-xl">
                                    <h3 class="text-lg font-semibold text-slate-700 mb-6">Operating Hours</h3>
                                    <div class="space-y-4" id="weekly-hours-container"></div>
                                </div>
                            </div>

                            <div class="mt-12 flex flex-col-reverse sm:flex-row sm:justify-between items-center gap-4">
                                <button type="button" data-prev="1" class="w-full sm:w-auto prev-btn btn-secondary px-6 py-3 text-sm font-semibold rounded-xl">Back</button>
                                <button type="button" data-next="3" class="w-full sm:w-auto next-btn btn-primary px-8 py-3 text-sm font-semibold text-white rounded-xl">
                                    <span class="btn-text">Next Step</span>
                                    <span class="btn-loading hidden"><span class="loading-spinner"></span> Processing...</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step 4: Staffing & Revenue -->
                    <div id="step-4" class="step-section">
                        <div class="max-w-4xl mx-auto">
                            <div class="mb-10">
                                <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Staffing & Revenue</h2>
                                <p class="text-slate-500 text-lg">Estimate your weekly operational needs.</p>
                            </div>
                        <div class="space-y-8">
                            <div class="p-6 bg-slate-50 rounded-xl border border-slate-200 space-y-6"><h3 class="text-lg font-semibold text-slate-800">Weekday Estimates (Mon-Fri)</h3><div class="form-group"><label for="weekday-revenue" class="block text-sm font-semibold text-slate-700 mb-2">Estimated Daily Revenue</label><select id="weekday-revenue" name="weekdayRevenue" class="form-input block w-full px-4 py-2 border border-slate-300 bg-white rounded-xl shadow-sm"><option value="<£800">Less than £800</option><option value="£800-£1600">£800 - £1600</option><option value="£1600-£2500">£1600 - £2500</option><option value="£2500-£3500">£2500 - £3500</option><option value=">£3500">More than £3500</option></select></div><div class="form-group"><label class="block text-sm font-semibold text-slate-700 mb-2">Required Staff</label><div class="grid grid-cols-2 md:grid-cols-3 gap-4"><div><label class="text-xs text-slate-500">Managers</label><select name="weekdayManagers" class="form-input block w-full mt-1 py-2 px-3"><option>0</option><option>1</option><option>2</option><option>3+</option></select></div><div><label class="text-xs text-slate-500">Chefs</label><select name="weekdayChefs" class="form-input block w-full mt-1 py-2 px-3"><option>0</option><option>1</option><option>2</option><option>3</option><option>4</option><option>5+</option></select></div><div><label class="text-xs text-slate-500">Waiters</label><select name="weekdayWaiters" class="form-input block w-full mt-1 py-2 px-3"><option>0</option><option>1</option><option>2</option><option>3</option><option>4</option><option>5+</option></select></div><div><label class="text-xs text-slate-500">Bartenders</label><select name="weekdayBartenders" class="form-input block w-full mt-1 py-2 px-3"><option>0</option><option>1</option><option>2</option><option>3+</option></select></div><div><label class="text-xs text-slate-500">Kitchen Porters</label><select name="weekdayPorters" class="form-input block w-full mt-1 py-2 px-3"><option>0</option><option>1</option><option>2</option><option>3+</option></select></div><div><label class="text-xs text-slate-500">Hosts</label><select name="weekdayHosts" class="form-input block w-full mt-1 py-2 px-3"><option>0</option><option>1</option><option>2+</option></select></div></div></div></div>
                            <div class="p-6 bg-slate-50 rounded-xl border border-slate-200 space-y-6"><h3 class="text-lg font-semibold text-slate-800">Weekend Estimates (Sat-Sun)</h3><div class="form-group"><label for="weekend-revenue" class="block text-sm font-semibold text-slate-700 mb-2">Estimated Daily Revenue</label><select id="weekend-revenue" name="weekendRevenue" class="form-input block w-full px-4 py-2 border border-slate-300 bg-white rounded-xl shadow-sm"><option value="<£1000">Less than £1000</option><option value="£1000-£1800">£1000 - £1800</option><option value="£1800-£2500">£1800 - £2500</option><option value="£2500-£3500">£2500 - £3500</option><option value=">£3500">More than £3500</option></select></div><div class="form-group"><label class="block text-sm font-semibold text-slate-700 mb-2">Required Staff</label><div class="grid grid-cols-2 md:grid-cols-3 gap-4"><div><label class="text-xs text-slate-500">Managers</label><select name="weekendManagers" class="form-input block w-full mt-1 py-2 px-3"><option>0</option><option>1</option><option>2</option><option>3+</option></select></div><div><label class="text-xs text-slate-500">Chefs</label><select name="weekendChefs" class="form-input block w-full mt-1 py-2 px-3"><option>0</option><option>1</option><option>2</option><option>3</option><option>4</option><option>5+</option></select></div><div><label class="text-xs text-slate-500">Waiters</label><select name="weekendWaiters" class="form-input block w-full mt-1 py-2 px-3"><option>0</option><option>1</option><option>2</option><option>3</option><option>4</option><option>5+</option></select></div><div><label class="text-xs text-slate-500">Bartenders</label><select name="weekendBartenders" class="form-input block w-full mt-1 py-2 px-3"><option>0</option><option>1</option><option>2</option><option>3+</option></select></div><div><label class="text-xs text-slate-500">Kitchen Porters</label><select name="weekendPorters" class="form-input block w-full mt-1 py-2 px-3"><option>0</option><option>1</option><option>2</option><option>3+</option></select></div><div><label class="text-xs text-slate-500">Hosts</label><select name="weekendHosts" class="form-input block w-full mt-1 py-2 px-3"><option>0</option><option>1</option><option>2+</option></select></div></div></div></div>
                        </div>
                            <div class="mt-12 flex flex-col-reverse sm:flex-row sm:justify-between items-center gap-4">
                                <button type="button" data-prev="2" class="w-full sm:w-auto prev-btn btn-secondary px-6 py-3 text-sm font-semibold rounded-xl">Back</button>
                                <button type="button" data-next="4" class="w-full sm:w-auto next-btn btn-primary px-8 py-3 text-sm font-semibold text-white rounded-xl">
                                    <span class="btn-text">Next Step</span>
                                    <span class="btn-loading hidden"><span class="loading-spinner"></span> Processing...</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Step 5: Table Configuration -->
                    <div id="step-5" class="step-section">
                        <div class="max-w-4xl mx-auto">
                            <div class="mb-10">
                                <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Table Configuration</h2>
                                <p class="text-slate-500 text-lg">Design your restaurant's floor plan.</p>
                            </div>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <div class="space-y-6"><div class="form-group"><label class="block text-sm font-semibold text-slate-700 mb-2">Layout Style</label><div class="grid grid-cols-2 gap-4"><div class="selectable-option"><input type="radio" id="layout-section" name="layoutStyle" value="section" class="hidden" checked><label for="layout-section" class="block text-center p-2.5 border-2 border-slate-200 rounded-xl cursor-pointer font-medium"><i data-lucide="layout-grid" class="mx-auto mb-2"></i>Section-based</label></div><div class="selectable-option"><input type="radio" id="layout-flow" name="layoutStyle" value="flow" class="hidden"><label for="layout-flow" class="block text-center p-2.5 border-2 border-slate-200 rounded-xl cursor-pointer font-medium"><i data-lucide="move-3d" class="mx-auto mb-2"></i>Free Flow</label></div></div></div><div id="section-based-ui"><button type="button" id="show-add-section-modal-btn" class="w-full btn-secondary py-2 text-sm font-semibold rounded-lg flex items-center justify-center gap-2"><i data-lucide="plus" class="w-4 h-4"></i> Add New Section</button><div id="table-sections-container" class="mt-4 space-y-4 max-h-[400px] overflow-y-auto pr-2"></div></div><div id="flow-based-ui" class="hidden"><div class="flex justify-between items-center"><h3 class="text-lg font-semibold text-slate-700">Tables</h3><button type="button" id="add-flow-table-btn" class="btn-secondary px-4 py-2 text-sm font-semibold text-slate-700 rounded-lg flex items-center gap-2"><i data-lucide="plus" class="w-4 h-4"></i> Add Table</button></div><div id="flow-table-list" class="mt-4 space-y-3 max-h-[400px] overflow-y-auto pr-2"></div></div></div>
                            <div class="space-y-4"><h3 class="text-lg font-semibold text-slate-700">Floor Plan Preview</h3><div id="floor-plan-preview" class="bg-gradient-to-br from-slate-50 to-slate-100 border-2 border-dashed border-slate-300 rounded-xl p-4 min-h-[400px]"></div></div>
                        </div>
                            <div class="mt-12 flex flex-col-reverse sm:flex-row sm:justify-between items-center gap-4">
                                <button type="button" data-prev="3" class="w-full sm:w-auto prev-btn btn-secondary px-6 py-3 text-sm font-semibold rounded-xl">Back</button>
                                <button type="button" data-next="5" class="w-full sm:w-auto next-btn btn-primary px-8 py-3 text-sm font-semibold text-white rounded-xl">
                                    <span class="btn-text">Next Step</span>
                                    <span class="btn-loading hidden"><span class="loading-spinner"></span> Processing...</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step 6: Review & Confirm -->
                    <div id="step-6" class="step-section">
                        <div class="max-w-4xl mx-auto">
                            <div class="mb-10">
                                <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Review & Confirm</h2>
                                <p class="text-slate-500 text-lg">Please review all your information before proceeding.</p>
                            </div>
                        <div id="summary-container" class="space-y-6 text-sm bg-gradient-to-br from-slate-50 to-slate-100 p-8 rounded-xl shadow-sm"></div>
                        <div class="mt-8">
                            <div class="flex items-center mb-6"><input type="checkbox" id="terms-checkbox" class="w-5 h-5 text-blue-600 border-slate-300 rounded focus:ring-blue-500"><label for="terms-checkbox" class="ml-3 text-sm text-slate-600">I agree to the <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Terms of Service</a> and <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Privacy Policy</a></label></div>
                                <div class="mt-12 flex flex-col-reverse sm:flex-row sm:justify-between items-center gap-4">
                                    <button type="button" data-prev="4" class="w-full sm:w-auto prev-btn btn-secondary px-6 py-3 text-sm font-semibold rounded-xl">Back</button>
                                    <button type="button" data-next="6" class="w-full sm:w-auto next-btn btn-primary px-8 py-3 text-sm font-semibold text-white rounded-xl">
                                        <span class="btn-text">Confirm & Continue</span>
                                        <span class="btn-loading hidden"><span class="loading-spinner"></span> Processing...</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 7: Personal Details -->
                    <div id="step-7" class="step-section">
                        <div class="max-w-4xl mx-auto">
                            <div class="mb-10">
                                <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Your Personal Details</h2>
                                <p class="text-slate-500 text-lg">We need this to verify your account.</p>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="form-group">
                                    <label for="first-name" class="block text-sm font-semibold text-slate-700 mb-2">First Name</label>
                                    <input type="text" id="first-name" name="firstName" required
                                           class="form-input block w-full px-4 py-3 border border-slate-300 rounded-xl shadow-sm"
                                           placeholder="Your first name">
                                    <div class="validation-message"></div>
                                </div>
                                <div class="form-group">
                                    <label for="last-name" class="block text-sm font-semibold text-slate-700 mb-2">Last Name</label>
                                    <input type="text" id="last-name" name="lastName" required
                                           class="form-input block w-full px-4 py-3 border border-slate-300 rounded-xl shadow-sm"
                                           placeholder="Your last name">
                                    <div class="validation-message"></div>
                                </div>
                                <div class="form-group md:col-span-2">
                                    <label for="phone" class="block text-sm font-semibold text-slate-700 mb-2">Phone Number</label>
                                    <input type="tel" id="phone" name="phone" required
                                           class="form-input block w-full px-4 py-3 border border-slate-300 rounded-xl shadow-sm"
                                           placeholder="e.g. 07123456789">
                                    <div class="validation-message"></div>
                                </div>
                                <div class="form-group md:col-span-2">
                                    <label for="address" class="block text-sm font-semibold text-slate-700 mb-2">Address</label>
                                    <input type="text" id="address" name="address" required
                                           class="form-input block w-full px-4 py-3 border border-slate-300 rounded-xl shadow-sm"
                                           placeholder="Street address">
                                    <div class="validation-message"></div>
                                </div>
                                <div class="form-group">
                                    <label for="city" class="block text-sm font-semibold text-slate-700 mb-2">City</label>
                                    <input type="text" id="city" name="city" required
                                           class="form-input block w-full px-4 py-3 border border-slate-300 rounded-xl shadow-sm"
                                           placeholder="Your city">
                                    <div class="validation-message"></div>
                                </div>
                                <div class="form-group">
                                    <label for="postcode" class="block text-sm font-semibold text-slate-700 mb-2">Postcode</label>
                                    <input type="text" id="postcode" name="postcode" required
                                           class="form-input block w-full px-4 py-3 border border-slate-300 rounded-xl shadow-sm"
                                           placeholder="e.g. SW1A 0AA">
                                    <div class="validation-message"></div>
                                </div>
                            </div>
                            <div class="mt-12 flex flex-col-reverse sm:flex-row sm:justify-between items-center gap-4">
                                <button type="button" data-prev="5" class="w-full sm:w-auto prev-btn btn-secondary px-6 py-3 text-sm font-semibold rounded-xl">Back</button>
                                <button type="submit" class="w-full sm:w-auto btn-primary px-8 py-3 text-sm font-semibold text-white rounded-xl">
                                    <span class="btn-text">Create Account</span>
                                    <span class="btn-loading hidden"><span class="loading-spinner"></span> Processing...</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div id="add-section-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden"><div class="bg-white p-8 rounded-2xl shadow-2xl max-w-sm w-full mx-4"><h3 class="text-xl font-bold text-slate-800 mb-4">Add New Section</h3><div class="form-group"><label for="modal-section-name" class="block text-sm font-semibold text-slate-700 mb-2">Section Name</label><input type="text" id="modal-section-name" placeholder="e.g. Patio, Bar Area" class="form-input w-full py-2 px-4"></div><div class="mt-6 flex justify-end gap-4"><button type="button" id="cancel-add-section" class="btn-secondary px-4 py-2 text-sm font-semibold rounded-lg">Cancel</button><button type="button" id="confirm-add-section" class="btn-primary px-4 py-2 text-sm font-semibold rounded-lg">Add Section</button></div></div></div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            lucide.createIcons();

            const steps = document.querySelectorAll('.step-section');
            const stepIndicatorsContainer = document.getElementById('step-indicators-container');
            const nextButtons = document.querySelectorAll('.next-btn');
            const prevButtons = document.querySelectorAll('.prev-btn');
            const form = document.getElementById('signup-form');
            const progressText = document.getElementById('progress-text');
            let currentStep = 0;

            const stepConfig = [
                { id: 'account', label: 'Account'},
                { id: 'business', label: 'Business'},
                { id: 'operations', label: 'Operations'},
                { id: 'staffing', label: 'Staff & Revenue'},
                { id: 'tables', label: 'Tables'},
                { id: 'summary', label: 'Review'},
                { id: 'personal', label: 'Personal'}
            ];

            const validationRules = {
                email: { required: true, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: 'Please enter a valid email address' },
                password: { required: true, minLength: 8, message: 'Password must be at least 8 characters long' },
                businessName: { required: true, minLength: 2, message: 'Business name must be at least 2 characters' },
                businessType: { required: true, message: 'Please select a business type' },
                firstName: { required: true, minLength: 2, message: 'First name must be at least 2 characters' },
                lastName: { required: true, minLength: 2, message: 'Last name must be at least 2 characters' },
                phone: { required: true, pattern: /^(?:(?:\(?(?:0(?:0|11)\)?[\s-]?\(?|0)44\)?[\s-]?(?:\(?0\)?[\s-]?)?)|(?:\(?0))(?:(?:\d{5}\)?[\s-]?\d{4,5})|(?:\d{4}\)?[\s-]?(?:\d{5}|\d{3}[\s-]?\d{3}))|(?:\d{3}\)?[\s-]?\d{3}[\s-]?\d{3,4})|(?:\d{2}\)?[\s-]?\d{4}[\s-]?\d{4}))(?:[\s-]?\d{4})?$/, message: 'Please enter a valid UK phone number' },
                address: { required: true, minLength: 5, message: 'Please enter a valid address' },
                city: { required: true, minLength: 2, message: 'Please enter a valid city' },
                postcode: { required: true, pattern: /^([Gg][Ii][Rr] 0[Aa]{2})|((([A-Za-z][0-9]{1,2})|(([A-Za-z][A-Ha-hJ-Yj-y][0-9]{1,2})|(([A-Za-z][0-9][A-Za-z])|([A-Za-z][A-Ha-hJ-Yj-y][0-9][A-Za-z]?))))\s?[0-9][A-Za-z]{2})$/, message: 'Please enter a valid UK postcode' }
            };

            const validateField = (field) => {
                const fieldName = field.name;
                const fieldValue = field.value.trim();
                const rules = validationRules[fieldName];
                if (!rules) return true;
                const fieldGroup = field.closest('.form-group');
                const validationMessage = fieldGroup ? fieldGroup.querySelector('.validation-message') : null;
                field.classList.remove('error', 'success');
                if (validationMessage) validationMessage.innerHTML = '';
                if (rules.required && fieldValue === '') {
                    field.classList.add('error');
                    if (validationMessage) validationMessage.innerHTML = `<div class="error-message"><i data-lucide="alert-circle" class="w-3 h-3"></i> This field is required</div>`;
                    lucide.createIcons();
                    return false;
                }
                if (rules.minLength && fieldValue.length < rules.minLength) {
                    field.classList.add('error');
                    if (validationMessage) validationMessage.innerHTML = `<div class="error-message"><i data-lucide="alert-circle" class="w-3 h-3"></i> ${rules.message}</div>`;
                    lucide.createIcons();
                    return false;
                }
                if (rules.pattern && !rules.pattern.test(fieldValue)) {
                    field.classList.add('error');
                    if (validationMessage) validationMessage.innerHTML = `<div class="error-message"><i data-lucide="alert-circle" class="w-3 h-3"></i> ${rules.message}</div>`;
                    lucide.createIcons();
                    return false;
                }
                field.classList.add('success');
                if (validationMessage) validationMessage.innerHTML = `<div class="success-message"><i data-lucide="check-circle" class="w-3 h-3"></i> Looks good!</div>`;
                lucide.createIcons();
                return true;
            };

            const validateStep = (stepIndex) => {
                const currentStepElement = steps[stepIndex];
                const fields = currentStepElement.querySelectorAll('input[required], select[required], textarea[required]');
                let isValid = true;
                fields.forEach(field => {
                    if (!field.disabled && !validateField(field)) isValid = false;
                });
                if (stepIndex === 4) {
                    const layoutStyle = form.querySelector('input[name="layoutStyle"]:checked').value;
                    const tableCount = (layoutStyle === 'section') ? document.querySelectorAll('#table-sections-container .table-row').length : document.querySelectorAll('#flow-table-list .table-row').length;
                    if (tableCount === 0) {
                        alert('Please add at least one table before continuing.');
                        return false;
                    }
                }
                return isValid;
            };

            const updateProgress = () => {
                progressText.textContent = `Step ${currentStep + 1} of ${stepConfig.length} - ${stepConfig[currentStep].label}`;
                stepIndicatorsContainer.innerHTML = '';
                stepConfig.forEach((step, index) => {
                    const isCompleted = index < currentStep;
                    const isActive = index === currentStep;
                    const indicator = document.createElement('div');
                    indicator.className = 'step-indicator flex-1 flex flex-col items-center relative';
                    if(isCompleted) indicator.classList.add('completed');
                    if(isActive) indicator.classList.add('active');
                    let statusIcon = isCompleted ? '<i data-lucide="check" class="w-4 h-4"></i>' : `${index + 1}`;
                    indicator.innerHTML = `<div class="step-circle relative z-10 w-8 h-8 rounded-full flex items-center justify-center font-bold border-2 border-slate-300 bg-white flex-shrink-0">${statusIcon}</div><div class="step-label text-xs text-center mt-2 text-slate-500">${step.label}</div>`;
                    stepIndicatorsContainer.appendChild(indicator);
                    if (index < stepConfig.length - 1) {
                        const connector = document.createElement('div');
                        connector.className = 'step-connector h-1 w-full absolute top-4 left-1/2 transform -translate-y-1/2';
                        indicator.appendChild(connector);
                    }
                });
                lucide.createIcons();
            };

            const showStep = (stepIndex, isNavigatingBack = false) => {
                if (!isNavigatingBack && stepIndex > currentStep && !validateStep(currentStep)) return;
                const currentButton = document.querySelector(`#step-${currentStep + 1} .next-btn`);
                if (currentButton && !isNavigatingBack && stepIndex > currentStep) {
                    const btnText = currentButton.querySelector('.btn-text');
                    const btnLoading = currentButton.querySelector('.btn-loading');
                    btnText.classList.add('hidden');
                    btnLoading.classList.remove('hidden');
                    setTimeout(() => {
                        btnText.classList.remove('hidden');
                        btnLoading.classList.add('hidden');
                        performStepChange(stepIndex);
                    }, 800);
                } else {
                    performStepChange(stepIndex);
                }
            };

            const performStepChange = (stepIndex) => {
                if (stepIndex === 5) generateSummary();
                steps.forEach((step, index) => step.classList.toggle('active', index === stepIndex));
                currentStep = stepIndex;
                updateProgress();
            };

            nextButtons.forEach(button => button.addEventListener('click', () => showStep(parseInt(button.dataset.next))));
            prevButtons.forEach(button => button.addEventListener('click', () => showStep(parseInt(button.dataset.prev), true)));
            form.addEventListener('input', (e) => { if (e.target.matches('input, select, textarea')) validateField(e.target); });

            const passwordField = form.querySelector('input[name="password"]');
            const passwordStrength = passwordField.closest('.form-group').querySelector('.password-strength');
            passwordField.addEventListener('input', () => {
                const password = passwordField.value;
                const strengthBars = passwordStrength.querySelectorAll('.strength-bar');
                const strengthText = passwordStrength.querySelector('.strength-text');
                if (password.length === 0) { passwordStrength.classList.add('hidden'); return; }
                passwordStrength.classList.remove('hidden');
                let strength = 0;
                if (password.length >= 8) strength++; if (/[A-Z]/.test(password)) strength++; if (/[a-z]/.test(password)) strength++; if (/[0-9]/.test(password)) strength++; if (/[^A-Za-z0-9]/.test(password)) strength++;
                const strengthLevels = { 1: 'bg-red-400', 2: 'bg-red-400', 3: 'bg-yellow-400', 4: 'bg-blue-400', 5: 'bg-green-400' };
                const strengthLabels = { 1: 'Very Weak', 2: 'Weak', 3: 'Fair', 4: 'Good', 5: 'Strong' };
                strengthBars.forEach((bar, i) => bar.className = `strength-bar h-1 rounded flex-1 ${i < strength ? strengthLevels[strength] : 'bg-slate-200'}`);
                strengthText.textContent = `Password strength: ${strengthLabels[strength] || ''}`;
            });
            form.querySelector('.toggle-password').addEventListener('click', (e) => {
                const btn = e.currentTarget;
                const icon = btn.querySelector('i');
                const isPassword = passwordField.type === 'password';
                passwordField.type = isPassword ? 'text' : 'password';
                icon.setAttribute('data-lucide', isPassword ? 'eye-off' : 'eye');
                lucide.createIcons();
            });

            const logoUpload = document.getElementById('logo-upload'), logoFile = document.getElementById('logo-file'), logoPreview = document.getElementById('logo-preview');
            logoUpload.addEventListener('click', () => logoFile.click());
            logoUpload.addEventListener('dragover', (e) => { e.preventDefault(); logoUpload.classList.add('dragover'); });
            logoUpload.addEventListener('dragleave', () => logoUpload.classList.remove('dragover'));
            logoUpload.addEventListener('drop', (e) => { e.preventDefault(); logoUpload.classList.remove('dragover'); if (e.dataTransfer.files.length) handleLogoFile(e.dataTransfer.files[0]); });
            logoFile.addEventListener('change', (e) => { if (e.target.files.length) handleLogoFile(e.target.files[0]); });
            const handleLogoFile = (file) => {
                if (!file.type.startsWith('image/')) return;
                const reader = new FileReader();
                reader.onload = (e) => {
                    logoPreview.querySelector('img').src = e.target.result;
                    logoPreview.querySelector('#logo-name').textContent = file.name;
                    logoPreview.querySelector('#logo-size').textContent = `${(file.size / 1024).toFixed(1)} KB`;
                    logoPreview.classList.remove('hidden');
                    logoUpload.classList.add('hidden');
                };
                reader.readAsDataURL(file);
            };
            document.getElementById('remove-logo').addEventListener('click', () => { logoFile.value = ''; logoPreview.classList.add('hidden'); logoUpload.classList.remove('hidden'); });

            const minPriceSlider = document.getElementById('min-price'), maxPriceSlider = document.getElementById('max-price'), minPriceDisplay = document.getElementById('min-price-display'), maxPriceDisplay = document.getElementById('max-price-display'), priceSliderRange = document.getElementById('price-slider-range');
            function updatePriceSlider() { let minVal = parseInt(minPriceSlider.value), maxVal = parseInt(maxPriceSlider.value); if (minVal >= maxVal) { minVal = maxVal - 1; minPriceSlider.value = minVal; } minPriceDisplay.textContent = `£${minVal}`; maxPriceDisplay.textContent = `£${maxVal}`; const minPercent = (minVal / minPriceSlider.max) * 100, maxPercent = (maxVal / maxPriceSlider.max) * 100; priceSliderRange.style.left = `${minPercent}%`; priceSliderRange.style.width = `${maxPercent - minPercent}%`; }
            minPriceSlider.addEventListener('input', updatePriceSlider); maxPriceSlider.addEventListener('input', updatePriceSlider); updatePriceSlider();

            const weeklyHoursContainer = document.getElementById('weekly-hours-container'), daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
            const createDayRow = (day, index) => { const isWeekend = day === 'Saturday' || day === 'Sunday'; const row = document.createElement('div'); row.className = 'day-hours-row grid grid-cols-12 items-center gap-4 p-3 bg-white border border-slate-200 rounded-xl'; row.innerHTML = `<div class="col-span-3 flex items-center"><input type="checkbox" id="open-${day}" name="open-${day}" class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500" ${!isWeekend ? 'checked' : ''}><label for="open-${day}" class="ml-3 font-semibold text-slate-700">${day}</label></div><div class="col-span-8 grid grid-cols-2 gap-4 time-inputs"><input type="time" name="openTime-${day}" value="09:00" class="form-input w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm" ${isWeekend ? 'disabled' : ''}><input type="time" name="closeTime-${day}" value="22:00" class="form-input w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm" ${isWeekend ? 'disabled' : ''}></div><div class="col-span-1">${index === 0 ? `<button type="button" title="Copy to all open days" class="copy-hours-btn text-slate-400 hover:text-blue-600 p-2 rounded-lg hover:bg-blue-50"><i data-lucide="copy" class="w-4 h-4"></i></button>` : ''}</div>`; weeklyHoursContainer.appendChild(row); const checkbox = row.querySelector(`#open-${day}`), timeInputs = row.querySelectorAll('.time-inputs input'); checkbox.addEventListener('change', () => { timeInputs.forEach(input => input.disabled = !checkbox.checked); }); if (index === 0) { row.querySelector('.copy-hours-btn').addEventListener('click', () => { const openTime = row.querySelector('input[type="time"][name^="openTime"]').value, closeTime = row.querySelector('input[type="time"][name^="closeTime"]').value; document.querySelectorAll('.day-hours-row').forEach(otherRow => { if (otherRow.querySelector('input[type="checkbox"]').checked) { otherRow.querySelector('input[type="time"][name^="openTime"]').value = openTime; otherRow.querySelector('input[type="time"][name^="closeTime"]').value = closeTime; } }); }); } };
            daysOfWeek.forEach(createDayRow);

            const layoutStyleRadios = form.querySelectorAll('input[name="layoutStyle"]'), sectionUI = document.getElementById('section-based-ui'), flowUI = document.getElementById('flow-based-ui'), floorPlanPreview = document.getElementById('floor-plan-preview'), sectionsContainer = document.getElementById('table-sections-container'), addFlowTableBtn = document.getElementById('add-flow-table-btn'), flowTableList = document.getElementById('flow-table-list'), addSectionModal = document.getElementById('add-section-modal'), showAddSectionModalBtn = document.getElementById('show-add-section-modal-btn'), cancelAddSectionBtn = document.getElementById('cancel-add-section'), confirmAddSectionBtn = document.getElementById('confirm-add-section'), modalSectionNameInput = document.getElementById('modal-section-name'); let tableIdCounter = 0, sectionIdCounter = 0;
            layoutStyleRadios.forEach(radio => { radio.addEventListener('change', () => { const isSection = radio.value === 'section'; sectionUI.classList.toggle('hidden', !isSection); flowUI.classList.toggle('hidden', isSection); updateFloorPlan(); }); });
            showAddSectionModalBtn.addEventListener('click', () => addSectionModal.classList.remove('hidden')); cancelAddSectionBtn.addEventListener('click', () => addSectionModal.classList.add('hidden')); confirmAddSectionBtn.addEventListener('click', () => { const name = modalSectionNameInput.value.trim(); if (name) { addSection(name); modalSectionNameInput.value = ''; addSectionModal.classList.add('hidden'); } });
            function addSection(name) { sectionIdCounter++; const sectionId = `section-${sectionIdCounter}`; const sectionDiv = document.createElement('div'); sectionDiv.className = 'table-section p-4 bg-white border border-slate-200 rounded-lg'; sectionDiv.dataset.sectionId = sectionId; sectionDiv.innerHTML = `<div class="flex justify-between items-center mb-2"><h4 class="font-semibold text-slate-800">${name}</h4><div class="flex items-center gap-2"><label class="text-xs text-slate-500">Tables:</label><input type="number" value="0" min="0" class="table-count-input form-input w-20 text-sm py-1 px-2"></div></div><div class="table-list-in-section space-y-2"></div>`; sectionsContainer.appendChild(sectionDiv); const tableCountInput = sectionDiv.querySelector('.table-count-input'), tableContainer = sectionDiv.querySelector('.table-list-in-section'); tableCountInput.addEventListener('input', (e) => { const targetCount = parseInt(e.target.value) || 0; if (targetCount < 0) { e.target.value = 0; return; } const currentCount = tableContainer.children.length; if (targetCount > currentCount) { for (let i = 0; i < targetCount - currentCount; i++) { addTableRowToContainer(tableContainer); } } else if (targetCount < currentCount) { for (let i = 0; i < currentCount - targetCount; i++) { if (tableContainer.lastChild) { tableContainer.lastChild.remove(); } } } updateFloorPlan(); }); updateFloorPlan(); }
            function addTableRowToContainer(container, capacity = 4, type = 'Indoor') { tableIdCounter++; const tableRow = document.createElement('div'); tableRow.className = 'table-row grid grid-cols-12 items-center gap-2 p-2 rounded-lg bg-slate-50'; tableRow.innerHTML = `<input type="text" value="T${tableIdCounter}" name="tableName" class="col-span-4 form-input text-sm py-1 px-2"><input type="number" value="${capacity}" min="1" name="tableCapacity" class="col-span-3 form-input text-sm py-1 px-2"><select name="tableType" class="col-span-4 form-input text-sm py-1 px-2"><option ${type === 'Indoor' ? 'selected' : ''}>Indoor</option><option ${type === 'Outdoor' ? 'selected' : ''}>Outdoor</option><option ${type === 'Bar' ? 'selected' : ''}>Bar</option></select><button type="button" class="remove-table-btn text-slate-400 hover:text-red-500 col-span-1"><i data-lucide="x" class="w-4 h-4"></i></button>`; container.appendChild(tableRow); lucide.createIcons(); tableRow.addEventListener('input', updateFloorPlan); tableRow.querySelector('.remove-table-btn').addEventListener('click', () => { tableRow.remove(); const section = tableRow.closest('.table-section'); if(section){ const countInput = section.querySelector('.table-count-input'); countInput.value = parseInt(countInput.value) - 1; } updateFloorPlan(); }); updateFloorPlan(); }
            addFlowTableBtn.addEventListener('click', () => addTableRowToContainer(flowTableList));
            function updateFloorPlan() { floorPlanPreview.innerHTML = ''; const layoutStyle = form.querySelector('input[name="layoutStyle"]:checked').value; if (layoutStyle === 'section') { floorPlanPreview.innerHTML = '<div id="floor-plan-sections" class="flex flex-col gap-4"></div>'; const floorPlanSectionsContainer = document.getElementById('floor-plan-sections'); const sections = sectionsContainer.querySelectorAll('.table-section'); if(sections.length === 0) { floorPlanSectionsContainer.innerHTML = `<p class="text-center text-slate-400">Add sections and tables to see a preview</p>`; return; } sections.forEach(section => { const sectionName = section.querySelector('h4').textContent; const sectionPreview = document.createElement('div'); sectionPreview.className = 'border-2 border-dashed border-slate-300 rounded-xl p-4'; sectionPreview.innerHTML = `<h5 class="font-semibold text-slate-600 mb-3">${sectionName}</h5><div class="grid grid-cols-auto-fill-80 gap-4"></div>`; const grid = sectionPreview.querySelector('.grid'); section.querySelectorAll('.table-row').forEach(tableRow => { grid.appendChild(createTableElement(tableRow)); }); floorPlanSectionsContainer.appendChild(sectionPreview); }); } else { floorPlanPreview.innerHTML = '<div class="grid grid-cols-auto-fill-80 gap-4"></div>'; const grid = floorPlanPreview.querySelector('.grid'); const tables = flowTableList.querySelectorAll('.table-row'); if(tables.length === 0) { grid.innerHTML = `<p class="col-span-full text-center text-slate-400">Add tables to see a preview</p>`; return; } tables.forEach(tableRow => { grid.appendChild(createTableElement(tableRow)); }); } }
            function createTableElement(tableRow) { const name = tableRow.querySelector('input[name="tableName"]').value, capacity = tableRow.querySelector('input[name="tableCapacity"]').value, type = tableRow.querySelector('select[name="tableType"]').value, colors = { Indoor: 'bg-blue-100 border-blue-300 text-blue-800', Outdoor: 'bg-green-100 border-green-300 text-green-800', Bar: 'bg-yellow-100 border-yellow-300 text-yellow-800' }, tableElement = document.createElement('div'); tableElement.className = `border-2 rounded-lg p-2 text-center text-xs font-medium ${colors[type]}`; tableElement.innerHTML = `<div class="font-bold text-sm">${name}</div><div class="text-slate-600">${capacity} seats</div>`; return tableElement; }

            const generateSummary = () => { const summaryContainer = document.getElementById('summary-container'), formElements = form.elements, weeklyHours = daysOfWeek.map(day => ({ day, isOpen: form.querySelector(`#open-${day}`).checked, openTime: form.querySelector(`[name="openTime-${day}"]`).value, closeTime: form.querySelector(`[name="closeTime-${day}"]`).value })), data = { email: formElements.email.value, businessName: formElements.businessName.value, businessType: formElements.businessType.value, annualRevenue: formElements.annualRevenue.value, cuisines: Array.from(form.querySelectorAll('input[name="cuisine"]:checked')).map(el => el.value).join(', ') || 'Not specified', priceRange: `£${form.elements.minPrice.value} - £${form.elements.maxPrice.value}`, weeklyHours, weekdayRevenue: formElements.weekdayRevenue.value, weekendRevenue: formElements.weekendRevenue.value, weekdayStaff: { Managers: formElements.weekdayManagers.value, Chefs: formElements.weekdayChefs.value, Waiters: formElements.weekdayWaiters.value }, weekendStaff: { Managers: formElements.weekendManagers.value, Chefs: formElements.weekendChefs.value, Waiters: formElements.weekendWaiters.value }, tables: Array.from(document.querySelectorAll('#step-5 .table-row')).map(row => ({ name: row.querySelector('input[name="tableName"]').value, capacity: row.querySelector('input[name="tableCapacity"]').value, type: row.querySelector('select[name="tableType"]').value, section: row.closest('.table-section')?.querySelector('h4')?.textContent || 'Flow' }))}; let tablesHtml = data.tables.map(t => `<li class="flex justify-between py-2 px-3 bg-white rounded-lg border border-slate-200"><span class="font-medium">${t.name} (${t.type} / ${t.section})</span><span class="text-slate-500">${t.capacity} seats</span></li>`).join(''); if (!tablesHtml) tablesHtml = '<li class="text-slate-400 italic">No tables configured.</li>'; const totalCapacity = data.tables.reduce((sum, t) => sum + parseInt(t.capacity || 0), 0); let hoursHtml = data.weeklyHours.map(h => `<li class="flex justify-between py-1"><span class="font-medium">${h.day}</span><span class="text-slate-500">${h.isOpen ? `${h.openTime} - ${h.closeTime}` : 'Closed'}</span></li>`).join(''); summaryContainer.innerHTML = `<div class="space-y-6"><div class="bg-white p-6 rounded-xl border border-slate-200"><h4 class="font-bold text-slate-800 flex items-center gap-2 mb-4"><i data-lucide="user-check" class="w-5 h-5 text-blue-600"></i> Account & Business</h4><div class="grid grid-cols-1 sm:grid-cols-2 gap-4"><div><p class="text-xs text-slate-500 uppercase">Email</p><p class="font-semibold text-slate-700">${data.email}</p></div><div><p class="text-xs text-slate-500 uppercase">Business Name</p><p class="font-semibold text-slate-700">${data.businessName}</p></div><div><p class="text-xs text-slate-500 uppercase">Business Type</p><p class="font-semibold text-slate-700">${data.businessType}</p></div><div><p class="text-xs text-slate-500 uppercase">Annual Revenue</p><p class="font-semibold text-slate-700">${data.annualRevenue}</p></div></div></div><div class="bg-white p-6 rounded-xl border border-slate-200"><h4 class="font-bold text-slate-800 flex items-center gap-2 mb-4"><i data-lucide="utensils" class="w-5 h-5 text-green-600"></i> Operations</h4><div class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-6"><div><p class="text-xs text-slate-500 uppercase">Cuisines</p><p class="font-semibold text-slate-700">${data.cuisines}</p></div><div><p class="text-xs text-slate-500 uppercase">Price Range</p><p class="font-semibold text-slate-700">${data.priceRange}</p></div><div class="sm:col-span-2"><p class="text-xs text-slate-500 uppercase mb-2">Opening Hours</p><ul class="text-sm">${hoursHtml}</ul></div></div></div><div class="bg-white p-6 rounded-xl border border-slate-200"><h4 class="font-bold text-slate-800 flex items-center gap-2 mb-4"><i data-lucide="users" class="w-5 h-5 text-orange-600"></i> Staffing & Revenue</h4><div class="grid grid-cols-1 sm:grid-cols-2 gap-4"><div><p class="text-xs text-slate-500 uppercase">Weekday Revenue</p><p class="font-semibold text-slate-700">${data.weekdayRevenue}</p></div><div><p class="text-xs text-slate-500 uppercase">Weekend Revenue</p><p class="font-semibold text-slate-700">${data.weekendRevenue}</p></div><div><p class="text-xs text-slate-500 uppercase">Weekday Staff</p><p class="font-semibold text-slate-700">${Object.entries(data.weekdayStaff).map(([k,v]) => `${v} ${k}`).join(', ')}</p></div><div><p class="text-xs text-slate-500 uppercase">Weekend Staff</p><p class="font-semibold text-slate-700">${Object.entries(data.weekendStaff).map(([k,v]) => `${v} ${k}`).join(', ')}</p></div></div></div><div class="bg-white p-6 rounded-xl border border-slate-200"><h4 class="font-bold text-slate-800 flex items-center gap-2 mb-4"><i data-lucide="layout" class="w-5 h-5 text-purple-600"></i> Table Configuration</h4><div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4"><div class="text-center p-3 bg-slate-50 rounded-lg"><p class="text-2xl font-bold text-slate-800">${data.tables.length}</p><p class="text-xs text-slate-500">Total Tables</p></div><div class="text-center p-3 bg-slate-50 rounded-lg"><p class="text-2xl font-bold text-slate-800">${totalCapacity}</p><p class="text-xs text-slate-500">Total Seats</p></div><div class="text-center p-3 bg-slate-50 rounded-lg"><p class="text-2xl font-bold text-slate-800">${data.tables.length > 0 ? (totalCapacity / data.tables.length).toFixed(1) : 0}</p><p class="text-xs text-slate-500">Avg. per Table</p></div></div><ul class="space-y-2">${tablesHtml}</ul></div></div>`; lucide.createIcons(); };

            form.addEventListener('submit', (e) => {
                e.preventDefault();
                if (!validateStep(currentStep)) return;
                if (!document.getElementById('terms-checkbox').checked) {
                    alert('Please accept the Terms of Service and Privacy Policy.');
                    return;
                }
                const submitBtn = form.querySelector('button[type="submit"]');
                const btnText = submitBtn.querySelector('.btn-text');
                const btnLoading = submitBtn.querySelector('.btn-loading');
                btnText.classList.add('hidden');
                btnLoading.classList.remove('hidden');
                submitBtn.disabled = true;
                setTimeout(() => {
                    const userData = { email: form.elements.email.value, businessName: form.elements.businessName.value, firstName: form.elements.firstName.value, signupDate: new Date().toISOString() };
                    localStorage.setItem('restroManagerUser', JSON.stringify(userData));
                    window.dispatchEvent(new CustomEvent('userRegistered', { detail: userData }));
                    const successModal = document.createElement('div');
                    successModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                    successModal.innerHTML = `<div class="bg-white p-8 rounded-2xl shadow-2xl max-w-md mx-4 text-center"><div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"><i data-lucide="check" class="w-8 h-8 text-green-600"></i></div><h3 class="text-2xl font-bold text-slate-800 mb-2">Account Created!</h3><p class="text-slate-600 mb-6">Welcome to RestroManager, ${userData.firstName}!</p><button id="redirect-dashboard" class="btn-primary w-full py-3 text-white rounded-xl font-semibold">Go to Dashboard</button><p class="text-xs text-slate-400 mt-3">Redirecting in <span id="countdown">5</span>s...</p></div>`;
                    document.body.appendChild(successModal);
                    lucide.createIcons();
                    let countdown = 5;
                    const countdownEl = document.getElementById('countdown');
                    const interval = setInterval(() => {
                        countdown--;
                        countdownEl.textContent = countdown;
                        if (countdown <= 0) {
                            clearInterval(interval);
                            console.log("Redirecting to dashboard...");
                        }
                    }, 1000);
                    document.getElementById('redirect-dashboard').addEventListener('click', () => {
                        clearInterval(interval);
                        console.log("Redirecting to dashboard...");
                    });
                }, 2000);
            });

            updateProgress();
            addSection('Main Dining');
        });
    </script>
</body>
</html>